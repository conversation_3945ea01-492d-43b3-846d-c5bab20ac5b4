import {
  AvatarQuality,
  StreamingEvents,
  VoiceChatTransport,
  VoiceEmotion,
  StartAvatarRequest,
  STTProvider,
  ElevenLabsModel,
} from "@heygen/streaming-avatar";
import { useEffect, useRef, useState } from "react";
import { useMemoizedFn, useUnmount } from "ahooks";

import { AvatarVideo } from "./AvatarSession/AvatarVideo";
import { useStreamingAvatarSession } from "./logic/useStreamingAvatarSession";
import { AvatarControls } from "./AvatarSession/AvatarControls";
import { useVoiceChat } from "./logic/useVoiceChat";
import { StreamingAvatarProvider, StreamingAvatarSessionState } from "./logic";
import { LoadingIcon } from "./Icons";
import { MessageHistory } from "./AvatarSession/MessageHistory";
import Avatar from "../public/Svg/home_avatar.svg";
import BackgroundImage from "../public/Svg/background_image.svg";
import Mic from "../public/Svg/mic.svg";
import Speaker from "../public/Svg/speaker.svg";
import style from "../styles/commonStyle.module.css";

import { AVATARS, STT_LANGUAGE_LIST } from "@/app/lib/constants";
import Image from "next/image";
import clsx from "clsx";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import SendIcon from "../public/Svg/send.svg";
import AppButton from "./UI/CommonUI/AppButton";
import { useAuthContext } from "./Prividers/AuthProvider";
import { useMessageHistory, MessageSender } from "./logic/useMessageHistory";

const DEFAULT_CONFIG: StartAvatarRequest = {
  quality: AvatarQuality.Low,
  avatarName: AVATARS[0].avatar_id,
  knowledgeId: undefined,
  voice: {
    rate: 1.5,
    emotion: VoiceEmotion.EXCITED,
    model: ElevenLabsModel.eleven_flash_v2_5,
  },
  language: "en",
  voiceChatTransport: VoiceChatTransport.WEBSOCKET,
  sttSettings: {
    provider: STTProvider.DEEPGRAM,
  },
};

function InteractiveAvatar() {
  const { initAvatar, startAvatar, stopAvatar, sessionState, stream } =
    useStreamingAvatarSession();
  const { startVoiceChat } = useVoiceChat();
  const auth = useAuthContext();

  const mediaStream = useRef<HTMLVideoElement>(null);
  const sessionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-stop session after 10 minutes of inactivity to prevent credit drain
  const resetSessionTimeout = () => {
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    sessionTimeoutRef.current = setTimeout(() => {
      console.log("⏰ Auto-stopping session due to inactivity");
      stopAvatar();
    }, 10 * 60 * 1000); // 10 minutes
  };

  // ⚠️ DISABLED AUTO-START TO PREVENT CREDIT DRAIN
  // Session now starts only when user clicks "Start Session"
  const getPredefinedConfig = () => {
    if (!auth?.user) return null;
    return {
      quality: AvatarQuality.Low,
      avatarName:
        auth?.user.username == "<EMAIL>"
          ? AVATARS[0].avatar_id
          : AVATARS[1].avatar_id,
      voice: {
        rate: 0.8,
        emotion: VoiceEmotion.EXCITED,
        model:
          auth?.user.username == "<EMAIL>"
            ? ElevenLabsModel.eleven_multilingual_v2
            : ElevenLabsModel.eleven_flash_v2_5,
        ...(auth?.user.username == "<EMAIL>" && {
          voiceId: "e85822bd14e144e8b6fe73da2fb1085c",
        }),
      },
      language:
        auth?.user.username == "<EMAIL>" ? "es" : "en",
      voiceChatTransport: VoiceChatTransport.WEBSOCKET,
      sttSettings: {
        provider: STTProvider.DEEPGRAM,
      },
      activityIdleTimeout: 30, // comment this after demo
      knowledgeId: "",
      knowledgeBase: JSON.stringify({
        PERSONA:
          "Marianne is a virtual digital concierge receptionist designed to greet students warmly and offer academic/career support. She interacts with kindness, compassion, and warmth, always addressing users by their logged-in username while maintaining a friendly yet respectful tone.",
        PRIMARY_USE_CASES: {
          Personalized_Greeting: "Welcome users by name immediately upon login",
          "Wellness_Check-in":
            "Initiate short, caring conversations about their current state",
          Academic_Career_Support:
            "Guide students to relevant resources based on their needs",
          Progress_Transition:
            "Smoothly shift from conversation to support options",
        },
        DIALOGUE_TEMPLATES: {
          opening_intro: `Hi ${auth.user.displayName}! I'm Marianne, and I'm here to help guide and support you throughout your journey.`,
          wellness_checkin:
            "How are you doing today? How's college going lately? Any big updates like projects, internships, or exams?",
          support_transition:
            "I'm here to help whenever you need it. Would you like to head over to the Resume Builder, talk through Career Advising, or explore Admission Guidance today?",
          post_checkin_followup:
            "Thanks for sharing. Let's find the right support for you today.",
          service_confirmation:
            "Excellent choice! Taking you to ${selected_service} now...",
        },
        RESPONSE_RULES: [
          "ALWAYS address user by their logged-in username in greeting",
          "Maintain warm, compassionate tone throughout interaction",
          "Keep check-in questions brief but genuinely caring",
          "Present support options as clear choices after check-in",
          "Acknowledge user responses before transitioning",
          "Use positive reinforcement when users select services",
        ],
        SUPPORT_OPTIONS: [
          "Resume Builder & Career Advising",
          "Admission Guidance",
        ],
        CONVERSATION_FLOW: {
          mandatory_first_step: "Personalized_Greeting",
          sequence: ["Wellness_Check-in", "Academic_Career_Support"],
          transition_phrase: "post_checkin_followup",
        },
      }),
      // auth?.user.username == "<EMAIL>"
      //   ? JSON.stringify({
      //       PERSONA:
      //         "Pedro is a virtual academic assistant designed to help students stay on track with their coursework. She interacts formally but supportively, encouraging task completion while maintaining a respectful, professional tone. Always address users by their logged in username.",
      //       PRIMARY_USE_CASES: {
      //         Automated_Assignment_Alerts:
      //           "Inform users of pending assignments immediately upon login",
      //         Task_Breakdown:
      //           "List each assignment with due dates and provide brief descriptions",
      //         Time_Management:
      //           "Offer to help schedule reminders or suggest the next assignment to work on",
      //         Follow_up_Prompts:
      //           "Gently remind users in later sessions if they postpone assignments",
      //       },
      //       DIALOGUE_TEMPLATES: {
      //         opening_intro: `Welcome back, ${auth?.user.displayName}! It's time to embark on another productive session as we navigate your upcoming assignments together.`,
      //         return_after_absence:
      //           "Good day. Welcome back. I hope you've been well. I noticed it has been a few days since your last visit.",
      //         assignment_alert:
      //           "You currently have {count} pending assignments this week. Please review the details below:\n\n{assignment_list}\n\nWould you like to begin working on one of them now, or should I remind you later today?",
      //         postpone_response:
      //           "Understood. I will send you a reminder in {reminder_time}. Please be mindful of approaching deadlines to stay on track with your progress.",
      //         start_assignment_response:
      //           "Excellent choice. Starting this module now will give you ample time to review and refine your summary before submission. Launching {module_name} now...",
      //         exit_reminder:
      //           "Thank you. I've saved your progress. You still have the {pending_assignment} pending, due {due_date}. I'll remind you again tomorrow. Have a productive day.",
      //       },
      //       RESPONSE_RULES: [
      //         "ALWAYS address user by their logged-in username",
      //         "Present assignments in clear format: [Assignment Name] - [Due Date]",
      //         "Offer concrete next-step options (start now/schedule reminder)",
      //         "Maintain formal but supportive tone",
      //         "Track assignment completion status",
      //         "Provide specific timeframes for reminders",
      //       ],
      //       ASSIGNMENT_FORMAT: [
      //         "Resume Building – Module 2: Writing a Professional Summary\n Due: Friday, June 28th",
      //         "Interview Skills Quiz – Practice Assessment\n Due: Sunday, June 30th",
      //       ],
      //       REMINDER_OPTIONS: {
      //         default_reminder_delay: "4 hours",
      //         follow_up_times: ["later today", "tomorrow", "in two days"],
      //       },
      //     })
      //   : auth?.user.username == "<EMAIL>"
      //   ? JSON.stringify({
      //       PERSONA:
      //         "Marianne es una asistente virtual de carrera integrada en una plataforma para estudiantes o personas que buscan empleo. Realiza un seguimiento de las tendencias y oportunidades laborales según las preferencias guardadas del usuario, el contenido de su currículum o sus intereses profesionales. Al iniciar sesión, Marianne ofrece sugerencias de trabajo personalizadas, fomenta la participación y ofrece asistencia para solicitar empleo o actualizar su currículum. Siempre diríjase al usuario por su nombre de usuario registrado.",
      //       PRIMARY_USE_CASES: {
      //         Personalized_Job_Discovery:
      //           "Notificar a los usuarios sobre nuevas ofertas de trabajo que coincidan con su perfil (por ejemplo, industria, ubicación, conjunto de habilidades)",
      //         "Re-engagement_After_Inactivity":
      //           "Proporcionar un registro cálido y actualizaciones sobre nuevas oportunidades cuando los usuarios regresan después de una ausencia",
      //         Resume_Readiness_Prompt:
      //           "Ofrecer revisar o actualizar el currículum del usuario para que coincida con las ofertas de trabajo actuales",
      //         Actionable_Job_Suggestions:
      //           "Proporcionar títulos de trabajo resumidos con opciones para guardar, rastrear o aplicar",
      //       },
      //       DIALOGUE_EXAMPLES: [
      //         {
      //           context: "User returns after absence",
      //           lines: [
      //             "Hola, me alegra verte de nuevo. Ha pasado un tiempo. ¿Cómo has estado?",
      //             "Durante su tiempo fuera, encontré 8 nuevas oportunidades laborales de HVAC en el área de Dallas que coinciden estrechamente con sus habilidades y preferencias.",
      //             "Oportunidades de muestra:",
      //             "Técnico de servicio de HVAC – Sistemas de enfriamiento de precisión",
      //             "Instalador de HVAC – NorthStar Mechanical",
      //             "¿Quiere ver la lista completa o adaptar su currículum para uno de estos puestos?",
      //           ],
      //         },
      //       ],
      //       RESPONSE_RULES: [
      //         "SIEMPRE diríjase al usuario por su nombre de usuario registrado",
      //         "Priorizar las oportunidades de trabajo recientes que coincidan con el perfil del usuario",
      //         "Sugerir actualizaciones del currículum cuando sean relevantes para nuevas oportunidades",
      //         "Ofrecer opciones claras para los siguientes pasos después de presentar la información",
      //       ],
      //       JOB_SUGGESTION_TEMPLATE: {
      //         opening: `Bienvenido de nuevo, ${auth?.user.displayName}. Es genial verte de nuevo. ¿Estás listo para descubrir nuevas y emocionantes oportunidades laborales diseñadas especialmente para ti?`,
      //         reengagement:
      //           "Hola, me alegra volver a verte. Ha pasado un tiempo. ¿Cómo has estado?",
      //         opportunity_announcement:
      //           "Durante su ausencia, encontré {count} nuevas oportunidades laborales en {industry} en el área de {location} que se ajustan estrechamente a sus habilidades y preferencias.",
      //       },
      //     })
      //   : JSON.stringify({
      //       PERSONA:
      //         "Marianne is a virtual career assistant embedded in a student or job-seeker platform. She tracks job trends and opportunities based on the user's saved preferences, resume content, or career interests. When a user logs in, Zara delivers personalized job suggestions, gently nudges engagement, and offers assistance in applying or updating their resume accordingly. Always address the user by their logged in user name.",
      //       PRIMARY_USE_CASES: {
      //         Personalized_Job_Discovery:
      //           "Notify users about new job listings that align with their profile (e.g., industry, location, skill set)",
      //         "Re-engagement_After_Inactivity":
      //           "Provide warm check-in and updates on new opportunities when users return after absence",
      //         Resume_Readiness_Prompt:
      //           "Offer to review or update user's resume to match current job listings",
      //         Actionable_Job_Suggestions:
      //           "Provide summarized job titles with options to save, track, or apply",
      //       },
      //       DIALOGUE_EXAMPLES: [
      //         {
      //           context: "User returns after absence",
      //           lines: [
      //             "Hello, it's good to see you again. It's been a while — how have you been?",
      //             "During your time away, I've found 8 new HVAC job opportunities in the Dallas area that closely match your skills and preferences.",
      //             "Sample Opportunities:",
      //             "HVAC Service Technician – Precision Cooling Systems",
      //             "HVAC Installer – NorthStar Mechanical",
      //             "Would you like to view the full list, or have me tailor your resume for one of these positions?",
      //           ],
      //         },
      //       ],
      //       RESPONSE_RULES: [
      //         "ALWAYS address user by their logged-in username",
      //         "Prioritize recent job opportunities matching user's profile",
      //         "Suggest resume updates when relevant to new opportunities",
      //         "Provide clear next-step options after presenting information",
      //       ],
      //       JOB_SUGGESTION_TEMPLATE: {
      //         opening: `Welcome back, ${auth?.user.displayName} It's great to see you again—are you ready to uncover some exciting new job opportunities tailored just for you?`,
      //         reengagement:
      //           "Hello, it's good to see you again. It's been a while — how have you been?",
      //         opportunity_announcement:
      //           "During your time away, I've found {count} new {industry} job opportunities in the {location} area that closely match your skills and preferences.",
      //       },
      //     })
    };
  };

  async function fetchAccessToken() {
    try {
      const response = await fetch("/api/get-access-token", {
        method: "POST",
      });
      const token = await response.text();

      console.log("Access Token:", token); // Log the token to verify

      return token;
    } catch (error) {
      console.error("Error fetching access token:", error);
      throw error;
    }
  }

  const startSessionV2 = useMemoizedFn(
    async (isVoiceChat: boolean, config: StartAvatarRequest) => {
      try {
        const newToken = await fetchAccessToken();
        const avatar = initAvatar(newToken);

        avatar.on(StreamingEvents.AVATAR_START_TALKING, (e) => {
          console.log("Avatar started talking", e);
        });
        avatar.on(StreamingEvents.AVATAR_STOP_TALKING, (e) => {
          console.log("Avatar stopped talking", e);
        });
        avatar.on(StreamingEvents.STREAM_DISCONNECTED, () => {
          console.log("Stream disconnected");
        });
        avatar.on(StreamingEvents.STREAM_READY, (event) => {
          console.log(">>>>> Stream ready:", event.detail);
        });
        avatar.on(StreamingEvents.USER_START, (event) => {
          console.log(">>>>> User started talking:", event);
        });
        avatar.on(StreamingEvents.USER_STOP, (event) => {
          console.log(">>>>> User stopped talking:", event);
        });
        avatar.on(StreamingEvents.USER_END_MESSAGE, (event) => {
          console.log(">>>>> User end message:", event);
        });
        avatar.on(StreamingEvents.USER_TALKING_MESSAGE, (event) => {
          console.log(">>>>> User talking message:", event);
        });
        avatar.on(StreamingEvents.AVATAR_TALKING_MESSAGE, (event) => {
          console.log(">>>>> Avatar talking message:", event);
        });
        avatar.on(StreamingEvents.AVATAR_END_MESSAGE, (event) => {
          console.log(">>>>> Avatar end message:", event);
          const { messages } = useMessageHistory();
          const avatarMessages = messages?.filter(
            (message) => message.sender === MessageSender.AVATAR
          );
          console.log(">>>>> Avatar messages:", avatarMessages);
        });

        // Create a personalized config with user's display name
        const personalizedConfig = { ...config };

        await startAvatar(personalizedConfig);

        if (isVoiceChat) {
          await startVoiceChat();
        }
      } catch (error) {
        console.error("Error starting avatar session:", error);
      }
    }
  );

  useUnmount(() => {
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    stopAvatar();
  });

  useEffect(() => {
    if (stream && mediaStream.current) {
      mediaStream.current.srcObject = stream;
      mediaStream.current.onloadedmetadata = () => {
        mediaStream.current!.play();
      };
    }
  }, [mediaStream, stream]);

  return (
    <div className={style.homeBlur}>
      {/* {sessionState === StreamingAvatarSessionState.INACTIVE && (
        <div
          className="flex justify-content-center align-items-center absolute z-1 top-0 left-0 bottom-0 right-0"
          style={{backgroundColor: "#fff"}}
        >
          <div className="loader"></div>
        </div>
      )} */}

      <div className={clsx("flex gap-4", style.chatCard)}>
        {/* Left Side - Avatar Video and Controls (60%) */}
        <div
          className={clsx(
            "flex flex-column shadow-none overflow-hidden p-0 surface-card avatar-video-section",
            style.chatRightContainer
          )}
          style={{
            width: "65%",
            backgroundColor: "#fff",
            border: "1px solid #5151511a",
            height: "100%",
            maxHeight: "100%", // Prevent expansion
            minHeight: "0", // Allow shrinking
          }}
        >
          {/* Avatar Display Area */}
          <div
            className="relative overflow-hidden flex flex-column align-items-center justify-content-center"
            style={{
              backgroundColor: "transparent",
              borderRadius: "var(--radius-2xl) var(--radius-2xl) 0 0",
              minHeight: "0", // Allow flex shrinking
              flex: "1 1 0", // Allow growing and shrinking but start from 0
              maxHeight: "calc(100% - 80px)", // Reserve space for controls (80px)
            }}
          >
            {sessionState !== StreamingAvatarSessionState.INACTIVE ? (
              <AvatarVideo ref={mediaStream} />
            ) : (
              <div
                className="w-full h-full flex align-items-center justify-content-center"
                style={{ color: "#515151" }}
              >
                <div className="loader"></div>
                {/* <>{"Initializing your avatar..."}</> */}
                {/* <AvatarConfig config={config} onConfigChange={setConfig} /> */}
              </div>
            )}

            {/* Status Indicator */}
            <div
              className="absolute top-0 right-0 m-4 flex align-items-center"
              style={{ gap: "var(--space-2)" }}
            >
              <div
                className="w-3 h-3 border-round-full"
                style={{
                  backgroundColor:
                    sessionState === StreamingAvatarSessionState.CONNECTED
                      ? "var(--success-color)"
                      : sessionState === StreamingAvatarSessionState.INACTIVE
                      ? "var(--gray-400)"
                      : "var(--warning-color)",
                  boxShadow: "var(--shadow-sm)",
                }}
              />
              <span
                className="text-caption font-medium"
                style={{
                  color: "var(--text-secondary)",
                  backgroundColor: "var(--bg-primary)",
                  padding: "var(--space-1) var(--space-2)",
                  borderRadius: "var(--radius-md)",
                  boxShadow: "var(--shadow-sm)",
                }}
              >
                {sessionState === StreamingAvatarSessionState.CONNECTED
                  ? "Connected"
                  : sessionState === StreamingAvatarSessionState.INACTIVE
                  ? "Ready"
                  : "Connecting..."}
              </span>
            </div>
          </div>

          {/* Controls Area */}
          <div
            className="flex flex-column align-items-center justify-content-center w-full"
            style={{
              borderTop: "1px solid var(--border-light)",
              backgroundColor: "transparent",
              gap: "var(--space-4)",
              flexShrink: 0, // Prevent controls from shrinking
              minHeight: "80px", // Ensure minimum height for controls
              height: "80px", // Fixed height for controls area
            }}
          >
            {sessionState === StreamingAvatarSessionState.CONNECTED ? (
              <AvatarControls />
            ) : sessionState === StreamingAvatarSessionState.INACTIVE ? (
              <div
                className="flex flex-column align-items-center"
                style={{ gap: "var(--space-3)" }}
              >
                <Button
                  label="🎤 Start Voice Session"
                  size="large"
                  className="p-button-success"
                  onClick={() => {
                    const config = getPredefinedConfig();
                    if (config) {
                      startSessionV2(true, config);
                    }
                  }}
                  style={{
                    padding: "var(--space-3) var(--space-4)",
                    fontSize: "1rem",
                    fontWeight: "600",
                  }}
                />
                <div
                  className="text-sm text-center"
                  style={{ color: "var(--text-color-secondary)" }}
                >
                  💡 Click to start your session with Marianne
                </div>
              </div>
            ) : (
              // <div
              //   className="flex flex-column align-items-center"
              //   style={{ gap: "var(--space-4)" }}
              // >
              //   <h3
              //     className="text-heading-small text-center"
              //     style={{ color: "var(--text-primary)" }}
              //   >
              //     Choose Your Interaction Mode
              //   </h3>
              //   <div
              //     className="flex flex-wrap justify-content-center"
              //     style={{ gap: "var(--space-4)" }}
              //   >
              //     <Button
              //       onClick={() => startSessionV2(true)}
              //       className="p-button-lg"
              //       icon="pi pi-microphone"
              //       style={{
              //         padding: "var(--space-4) var(--space-6)",
              //         minWidth: "10rem",
              //         flex: "1 1 auto",
              //       }}
              //     >
              //       Voice Chat
              //     </Button>
              //     <Button
              //       onClick={() => startSessionV2(false)}
              //       className="p-button-secondary p-button-lg"
              //       icon="pi pi-comments"
              //       style={{
              //         padding: "var(--space-4) var(--space-6)",
              //         minWidth: "10rem",
              //         flex: "1 1 auto",
              //       }}
              //     >
              //       Text Chat
              //     </Button>
              //   </div>
              //   <p
              //     className="text-body-small text-center"
              //     style={{
              //       color: "var(--text-secondary)",
              //       maxWidth: "24rem",
              //       lineHeight: "var(--line-height-relaxed)",
              //     }}
              //   >
              //     Start a conversation with your AI avatar using voice or text.
              //     Configure your preferences above before starting.
              //   </p>
              // </div>
              <div
                className="flex flex-column align-items-center"
                style={{ gap: "var(--space-3)" }}
              >
                <LoadingIcon />
                <span
                  className="text-body-medium"
                  style={{ color: "var(--text-secondary)" }}
                >
                  Initializing your avatar...
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Right Side - Conversation History (60%) */}
        <div className={style.conversationContainer}>
          {sessionState === StreamingAvatarSessionState.CONNECTED ? (
            <MessageHistory />
          ) : (
            <div
              className="flex flex-column align-items-center justify-content-center  h-full"
              style={{
                backgroundColor: "transparent",
                border: "none",
                padding: "var(--space-8)",
                gap: "var(--space-4)",
              }}
            >
              <i
                className="pi pi-comments text-6xl"
                style={{ color: "var(--gray-400)" }}
              />
              <h3
                className="text-heading-medium text-center"
                style={{ color: "#515151" }}
              >
                Conversation
              </h3>
              <p
                className="text-body-medium text-center text-light"
                style={{
                  maxWidth: "20rem",
                  lineHeight: "var(--line-height-relaxed)",
                  color: "#515151",
                }}
              >
                Start a conversation with your avatar to see the chat history
                here
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function InteractiveAvatarWrapper() {
  return (
    <StreamingAvatarProvider basePath={process.env.NEXT_PUBLIC_BASE_API_URL}>
      <InteractiveAvatar />
    </StreamingAvatarProvider>
  );
}
