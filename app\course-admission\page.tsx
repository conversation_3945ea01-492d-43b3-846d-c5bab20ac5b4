"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "primereact/button";

export default function CourseAdmission() {
  const router = useRouter();

  return (
    <div
      className="w-full h-full flex flex-column align-items-center justify-content-center"
      style={{
        backgroundColor: "#f8f9fa",
        padding: "var(--space-8)",
        gap: "var(--space-6)",
      }}
    >
      <div
        className="flex flex-column align-items-center text-center"
        style={{ gap: "var(--space-4)", maxWidth: "600px" }}
      >
        <i
          className="pi pi-book text-6xl"
          style={{ color: "var(--primary-color)" }}
        />
        <h1
          className="text-4xl font-bold"
          style={{ color: "var(--text-primary)" }}
        >
          Course Admission Guidance
        </h1>
        <p
          className="text-lg"
          style={{
            color: "var(--text-secondary)",
            lineHeight: "1.6",
          }}
        >
          Get comprehensive guidance for course admissions, application processes,
          and enrollment requirements. We're here to help you navigate your
          educational journey.
        </p>
      </div>

      <div
        className="flex flex-column align-items-center"
        style={{ gap: "var(--space-4)" }}
      >
        <div
          className="p-6 border-round-lg shadow-2"
          style={{
            backgroundColor: "white",
            border: "1px solid var(--border-light)",
            minWidth: "400px",
          }}
        >
          <h3
            className="text-xl font-semibold mb-3"
            style={{ color: "var(--text-primary)" }}
          >
            Admission Services
          </h3>
          <ul
            className="list-none p-0 m-0"
            style={{ gap: "var(--space-2)" }}
          >
            <li className="flex align-items-center mb-2">
              <i className="pi pi-check-circle mr-2" style={{ color: "green" }} />
              Application Process Guidance
            </li>
            <li className="flex align-items-center mb-2">
              <i className="pi pi-check-circle mr-2" style={{ color: "green" }} />
              Course Requirements Information
            </li>
            <li className="flex align-items-center mb-2">
              <i className="pi pi-check-circle mr-2" style={{ color: "green" }} />
              Enrollment Assistance
            </li>
            <li className="flex align-items-center mb-2">
              <i className="pi pi-check-circle mr-2" style={{ color: "green" }} />
              Financial Aid Information
            </li>
            <li className="flex align-items-center mb-2">
              <i className="pi pi-check-circle mr-2" style={{ color: "green" }} />
              Course Selection Advice
            </li>
          </ul>
        </div>

        <Button
          label="Back to Chat"
          icon="pi pi-arrow-left"
          className="p-button-outlined"
          onClick={() => router.push("/")}
          style={{
            padding: "var(--space-3) var(--space-6)",
          }}
        />
      </div>
    </div>
  );
}
